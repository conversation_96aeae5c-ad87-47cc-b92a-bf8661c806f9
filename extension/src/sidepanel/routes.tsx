import React from 'react';
import * as I18nModule from '../utils/i18n.tsx';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { RemotePage } from './pages/RemotePage.tsx';
import { LoginPage } from './pages/Login.tsx';
import { ConversationPage } from './pages/ConversationPage.tsx';
import { MiniappsPage } from './pages/MiniappsPage.tsx';
import { WorkflowPage } from './pages/WorkflowPage.tsx';
import TestWebToolkit from './pages/TestWebToolkit.tsx';

export const AppRouter: React.FC = () => {
  const { I18nProvider } = I18nModule;
  return (
    <I18nProvider>
      <Router>
        <Routes>
          <Route path="/" element={<LoginPage />} />
          <Route path="/sidepanel.html" element={<LoginPage />} />
          <Route path="/conversation" element={<ConversationPage />} />
          <Route path="/miniapp" element={<MiniappsPage />} />
          <Route path="/workflow" element={<WorkflowPage />} />
          <Route path="/remote" element={<RemotePage />} />
          <Route
            path="/test-webtoolkit"
            element={<TestWebToolkit onBack={() => window.history.back()} />}
          />
        </Routes>
      </Router>
    </I18nProvider>
  );
};

export default AppRouter;
